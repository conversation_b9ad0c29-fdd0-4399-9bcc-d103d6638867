import React from 'react';
import { FileText, Link, Check, MoreHorizontal, ExternalLink } from 'lucide-react';
import { GoogleDocsUpdateDocumentOutput } from 'src/config/nangoModels';
import { ContextMenu, ContextMenuItem } from '../common/ContextMenu';
import { DropdownMenu } from '../common/DropdownMenu';

type GoogleDocsUpdateDocumentDisplayProps = {
  output: GoogleDocsUpdateDocumentOutput;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Google Docs document update result
 */
function GoogleDocsUpdateDocumentDisplay({
  output,
  actionParameters
}: GoogleDocsUpdateDocumentDisplayProps) {
  if (!output || !output.documentId) {
    return (
      <div className="p-6 text-center">
        <FileText className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No update information available</p>
      </div>
    );
  }

  const replyCount = output.replies?.length || 0;

  // Extract document name from action parameters if available
  const documentName = actionParameters?.documentName || output.title || 'Document Successfully Updated';

  const docUrl = `https://docs.google.com/document/d/${output.documentId}/edit`;
  const menuItems: ContextMenuItem[] = [
    {
      label: 'Open in Google Docs',
      href: docUrl,
      icon: <ExternalLink className="w-4 h-4 mr-2" />,
    },
  ];

  return (
    <ContextMenu items={menuItems}>
      <div>
        <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Check className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Google Docs Document Updated
              </h3>
            </div>
            <DropdownMenu
              trigger={
                <button
                  className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  title="More options"
                >
                  <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                </button>
              }
              items={menuItems}
            />
          </div>
        </div>

      <div className="p-5">
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3">
            <div className="w-10 h-10 rounded-md bg-green-100 dark:bg-green-900 flex items-center justify-center">
              <FileText className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div className="min-w-0 flex-1">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              {documentName}
            </h4>

            {replyCount > 0 && (
              <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center mb-1">
                  <span className="font-medium mr-2">Replies:</span>
                  <span>{replyCount}</span>
                </div>
              </div>
            )}

            {output.replies && output.replies.length > 0 && (
              <div className="mt-3">
                <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Reply Summary:
                </h5>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-md p-3 max-h-40 overflow-y-auto">
                  <pre className="text-xs font-mono text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                    {JSON.stringify(output.replies, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            <div className="mt-4" />
          </div>
        </div>
      </div>
    </div>
    </ContextMenu>
  );
}

export { GoogleDocsUpdateDocumentDisplay };
