import {
  SlackIcon,
  NotionIcon,
  LinkedInIcon,
  GmailIcon,
  GoogleDriveIcon,
  GoogleCalendarIcon,
  GoogleSheetsIcon,
  DropboxIcon,
  ZoomIcon,
  ShopifyIcon,
  GithubIcon,
} from 'components/icons/providers';

// Define provider types for type safety
export type ProviderType =
  | 'zoom'
  | 'slack'
  | 'shopify'
  | 'notion'
  | 'linkedin'
  | 'google-mail'
  | 'google-drive'
  | 'google-calendar'
  | 'google-sheet'
  | 'github'
  | 'dropbox';

// Icon lookup map for providers
export const ICON_LOOKUP: Record<ProviderType, JSX.Element> = {
  zoom: <ZoomIcon />,
  slack: <SlackIcon />,
  shopify: <ShopifyIcon />,
  notion: <NotionIcon />,
  linkedin: <LinkedInIcon />,
  'google-mail': <GmailIcon />,
  'google-calendar': <GoogleCalendarIcon />,
  'google-drive': <GoogleDriveIcon />,
  'google-sheet': <GoogleSheetsIcon />,
  github: <GithubIcon />,
  dropbox: <DropboxIcon />,
};

// Label overrides for sync keys
const SYNC_LABEL_OVERRIDES: Partial<Record<ProviderType, Record<string, string>>> = {
  zoom: {
    meetings: 'Meeting synced',
    recordings: 'Recording synced',
  },
  slack: {
    messages: 'Message synced',
    channels: 'Channel synced',
  },
  shopify: {
    products: 'Product synced',
    orders: 'Order synced',
  },
  notion: {
    pages: 'Page synced',
    databases: 'Database synced',
  },
  linkedin: {
    posts: 'Post synced',
    connections: 'Connection synced',
  },
  'google-mail': {
    labels: 'Label added',
    'emails-fork': 'Receive email',
  },
  'google-calendar': {
    events: 'Event synced',
    calendars: 'Calendar synced',
  },
  github: {
    repos: 'Repository synced',
    issues: 'Issue synced',
  },
  dropbox: {
    'files-fork': 'File synced',
  },
  'google-drive': {
    'documents-fork': 'Document added',
    files: 'File added',
    folders: 'Folder added',
  },
  'google-sheet': {
    'sheets-fork': 'Sheet added',
    'cells-fork': 'Cell updated',
  },
};

const ACTION_LABEL_OVERRIDES: Partial<Record<ProviderType, Record<string, string>>> = {
  'google-mail': {
    'send-email': 'Send email',
    'compose-draft': 'Compose draft',
    'compose-draft-reply': 'Draft reply',
  },
  github: {
    'create-pr': 'Create PR',
  },
  dropbox: {
    'upload-file': 'Upload file',
  },
};

function capitalize(word: string): string {
  if (!word) return '';
  return word.charAt(0).toUpperCase() + word.slice(1);
}

function singular(word: string): string {
  return word.endsWith('s') ? word.slice(0, -1) : word;
}

function defaultSyncLabel(key: string): string {
  return (
    key
      .split(/[_-]/)

      .map(w => capitalize(singular(w)))
      .join(' ') + ' synced'
  );
}

function defaultActionLabel(key: string): string {
  return key
    .split(/[_-]/)
    .map(w => capitalize(w))
    .join(' ');
}

export function getSyncLabel(provider: ProviderType, key: string): string {
  return SYNC_LABEL_OVERRIDES[provider]?.[key] ?? defaultSyncLabel(key);
}

export function getActionLabel(provider: ProviderType, key: string): string {
  return ACTION_LABEL_OVERRIDES[provider]?.[key] ?? defaultActionLabel(key);
}
